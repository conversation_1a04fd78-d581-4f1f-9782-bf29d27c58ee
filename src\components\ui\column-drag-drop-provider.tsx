// src/components/ui/column-drag-drop-provider.tsx
'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ColumnDragDropContext, useColumnDragDropContext } from '@/contexts/ColumnDragDropContext';
import { Table as ReactTableType } from '@tanstack/react-table';
import { CustomColumnDefinition, TaskColumnId } from '@/types';

interface ColumnDragDropProviderProps {
  children: React.ReactNode;
  tableInstance: ReactTableType<any>;
  onColumnOrderChange?: (columnOrder: string[]) => void;
  initialColumnOrder?: string[];
  disableDragDrop?: boolean;
}

/**
 * @component ColumnDragDropProvider
 * @description 提供表格列拖拽功能的上下文提供者组件
 */
export const ColumnDragDropProvider: React.FC<ColumnDragDropProviderProps> = ({
  children,
  tableInstance,
  onColumnOrderChange,
  initialColumnOrder,
  disableDragDrop = false,
}) => {
  // 当前列顺序
  const [columnOrder, setColumnOrder] = useState<string[]>(
    initialColumnOrder || tableInstance.getAllLeafColumns().map(column => column.id)
  );

  // 预览列顺序（拖拽过程中）
  const [previewColumnOrder, setPreviewColumnOrder] = useState<string[]>(columnOrder);

  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false);
  const [draggedColumnId, setDraggedColumnId] = useState<string | null>(null);
  const [dragOverColumnId, setDragOverColumnId] = useState<string | null>(null);

  // 处理列顺序变更
  const handleColumnOrderChange = useCallback((newOrder: string[]) => {
    setColumnOrder(newOrder);
    setPreviewColumnOrder(newOrder);
    onColumnOrderChange?.(newOrder);
  }, [onColumnOrderChange]);

  // 创建预览顺序
  const createPreviewOrder = useCallback((sourceId: string, targetId: string) => {
    if (!sourceId || !targetId || sourceId === targetId) return columnOrder;

    const currentOrder = [...columnOrder];
    const sourceIndex = currentOrder.indexOf(sourceId);
    const targetIndex = currentOrder.indexOf(targetId);

    if (sourceIndex === -1 || targetIndex === -1) return currentOrder;

    // 移除源列
    currentOrder.splice(sourceIndex, 1);
    // 在目标位置插入源列
    currentOrder.splice(targetIndex, 0, sourceId);

    return currentOrder;
  }, [columnOrder]);

  // 当拖拽状态变化时更新预览顺序
  useEffect(() => {
    if (draggedColumnId && dragOverColumnId) {
      const newPreviewOrder = createPreviewOrder(draggedColumnId, dragOverColumnId);
      setPreviewColumnOrder(newPreviewOrder);
    } else {
      setPreviewColumnOrder(columnOrder);
    }
  }, [draggedColumnId, dragOverColumnId, columnOrder, createPreviewOrder]);

  // 当表格实例或初始列顺序变化时重置列顺序
  useEffect(() => {
    if (initialColumnOrder) {
      setColumnOrder(initialColumnOrder);
      setPreviewColumnOrder(initialColumnOrder);
    }
  }, [initialColumnOrder, tableInstance]);

  // 从表格实例中提取列定义
  const columnDefinitions = tableInstance.getAllLeafColumns().map(column => {
    const meta = column.columnDef.meta as { customDef?: CustomColumnDefinition } | undefined;
    return meta?.customDef || { id: column.id, isReorderable: true };
  });

  // 上下文值
  const contextValue = {
    draggedColumnId,
    dragOverColumnId,
    isDragging,
    isColumnReorderable: (columnId: string) => {
      if (disableDragDrop) return false;
      const column = columnDefinitions.find(col => col.id === columnId);
      return column?.isReorderable !== false;
    },
    setDraggedColumnId,
    setDragOverColumnId,
    setIsDragging,
    columnOrder,
    updateColumnOrder: handleColumnOrderChange,
    previewColumnOrder,
    setPreviewColumnOrder,
    columnDefinitions: columnDefinitions.reduce<Record<string, CustomColumnDefinition>>(
      (acc, col) => {
        acc[col.id] = {
          ...col,
          label: ('label' in col ? col.label : col.id) as string,
          densityStyles: ('densityStyles' in col ? col.densityStyles : {})
        };
        return acc;
      },
      {}
    ),
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <ColumnDragDropContext.Provider value={{
        ...contextValue,
        setPreviewColumnOrder: (order: string[] | null) => setPreviewColumnOrder(order || [])
      }}>
        {children}
      </ColumnDragDropContext.Provider>
    </DndProvider>
  );
};