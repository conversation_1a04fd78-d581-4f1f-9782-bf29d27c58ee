// src/components/sections/task-list/task-list-card-view.tsx
'use client';

import React, { useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { Task, Vehicle, TaskListStoredSettings, TaskListDensityMode, VehicleDisplayMode } from '@/types';
import { TaskItemContent } from './task-item-content';
import { useAppStore } from '@/store/appStore';
import { useTaskSelectionActions, useTaskSelectionState } from '@/contexts/TaskSelectionContext';

interface TaskListCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  productionLineCount: number;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  dragOverTaskId: string | null;
  setDragOverTaskId: (id: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (id: string | null) => void;
  handleVehicleDrop: (event: React.DragEvent<HTMLDivElement>, productionLineId: string | null, taskId: string) => void;
  handleTaskContextMenu: (event: React.MouseEvent, taskId: string) => void;
  handleRowDoubleClick: (task: Task) => void;
  getStatusLabelProps: (status?: Task['dispatchStatus']) => { label: string; className: string };
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string, deliveryOrderId?: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicleId: string, productionLineId: string, taskId: string) => void;
}

export const TaskListCardView = React.memo(function TaskListCardView({
  filteredTasks,
  vehicles,
  settings,
  productionLineCount,
  vehicleDisplayMode,
  taskStatusFilter,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  handleVehicleDrop,
  handleTaskContextMenu,
  handleRowDoubleClick,
  getStatusLabelProps,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
}: TaskListCardViewProps) {
  const {
    density,
    inTaskVehicleCardStyles,
  } = settings;
  const selectedPlantId = useAppStore(state => state.selectedPlantId);
  const { setSelectedTask } = useTaskSelectionActions();
  const { isTaskSelected, selectedTaskId, selectedTask } = useTaskSelectionState();

  // 调试信息
  console.log('TaskListCardView - Current selection:', { selectedTaskId, selectedTask: selectedTask?.taskNumber });

  const handleCardClick = useCallback((task: Task, e: React.MouseEvent) => {
    console.log('=== Card Click Debug ===');
    console.log('Clicked task:', task.taskNumber, task.id);
    console.log('setSelectedTask function:', typeof setSelectedTask, setSelectedTask);

    // 避免在点击按钮或其他交互元素时触发卡片选中
    const target = e.target as HTMLElement;
    console.log('Click target:', target.tagName, target.className);

    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      console.log('Click ignored - target is interactive element');
      return;
    }

    // 切换选中状态：如果已选中则取消选中，否则选中
    const isCurrentlySelected = isTaskSelected(task.id);
    console.log('Current selection state for', task.id, ':', isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log('Deselecting task:', task.taskNumber);
      setSelectedTask(null);
    } else {
      console.log('Selecting task:', task.taskNumber);
      console.log('About to call setSelectedTask with:', task);
      setSelectedTask(task);
      console.log('setSelectedTask called');
    }
    console.log('=== End Card Click Debug ===');
  }, [setSelectedTask, isTaskSelected]);


  if (filteredTasks.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground p-4">
        {selectedPlantId ? '此搅拌站当前状态无任务。' : '请先选择一个搅拌站。'}
      </div>
    );
  }

  return (
    <div>
      {/* 测试按钮 */}
      <div style={{ padding: '20px', backgroundColor: 'yellow', margin: '10px' }}>
        <button
          onClick={() => {
            console.log('TEST BUTTON CLICKED');
            alert('Test button works!');
          }}
          style={{ padding: '10px', backgroundColor: 'green', color: 'white', border: 'none', cursor: 'pointer' }}
        >
          测试按钮 - 如果这个能点击，说明基本事件正常
        </button>

        <div
          onClick={() => {
            console.log('TEST DIV CLICKED');
            alert('Test div works!');
          }}
          style={{
            padding: '10px',
            backgroundColor: 'blue',
            color: 'white',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          测试DIV - 点击我
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-0.5 p-0.5">
        {filteredTasks.map((task) => {
        const isSelected = isTaskSelected(task.id);

        return (
          <div
            key={task.id}
            data-task-id={task.id}
            data-row-id={task.id}
            onClick={(e) => {
              console.log('=== DIV ONCLICK FIRED ===', task.taskNumber);
              alert(`Clicked task: ${task.taskNumber}`);
              handleCardClick(task, e);
            }}
            onMouseDown={(e) => {
              console.log('=== DIV MOUSEDOWN FIRED ===', task.taskNumber);
            }}
            style={{
              border: '2px solid red',
              padding: '10px',
              margin: '5px',
              cursor: 'pointer',
              backgroundColor: isSelected ? 'lightblue' : 'white'
            }}

          >
            <div style={{ padding: '10px' }}>
              <strong>{task.taskNumber}</strong> - {task.projectName}
              <br />
              Status: {task.dispatchStatus}
              <br />
              Selected: {isSelected ? 'YES' : 'NO'}
            </div>
          </div>

          </Card>
        );
      })}
    </div>
  );
});
TaskListCardView.displayName = 'TaskListCardView';
