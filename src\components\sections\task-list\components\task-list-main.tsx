// src/components/sections/task-list/components/task-list-main.tsx

import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { useDrop } from 'react-dnd';
import type { ColumnDef } from '@tanstack/react-table';
import { shallow } from 'zustand/shallow';
import { cn } from '@/lib/utils';

import type { Task, Vehicle, CustomColumnDefinition, StyleableColumnId } from '@/types';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { useFilteredTasks } from '@/hooks/useFilteredTasks';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { useTaskContextMenu } from '@/hooks/useTaskContextMenu';
import { useVehicleCardContextMenu } from '@/hooks/useVehicleCardContextMenu';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo';
import { useDraggableFab } from '@/hooks/useDraggableFab';
import { useToast } from '@/hooks/use-toast';

import { ALL_TASK_COLUMNS_CONFIG } from '../task-list.config';
import { getDensityStyles, getCardGridClasses } from '../task-list-density-config';
import { convertColumnTextStyles } from '../task-list-type-guards';
import { allTextColorOptionsMap, allBackgroundColorOptionsMap } from '@/components/modals/column-specific-style-modal';
import { ItemTypes } from '@/constants/dndItemTypes';
import { groupTasks } from '@/utils/task-grouping';
import { useReminderStore } from '@/store/reminderStore';

import { SectionContainer } from '@/components/shared/section-container';
import { TaskRowHighlightProvider } from '@/contexts/TaskRowHighlightContext';

// Import new components
import { useTaskCardConfigManager } from './task-card-config-manager';
import { useTaskListEventHandlers, useTaskListDragHandlers, useTaskListFABHandlers } from './task-list-event-handlers';
import { TaskListFAB } from './task-list-fab';
import { TaskListContentRenderer } from './task-list-content-renderer';
import { TaskListModalManager } from './task-list-modal-manager';

// Constants
const FAB_SIZE = 48;
const FAB_MARGIN = 16;

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

export function TaskListMain() {
  const { toast } = useToast();
  const { messages, markAsRead } = useReminderStore();
  
  // Store data
  const allTasks = useAppStore(state => state.tasks, shallow);
  const allVehicles = useAppStore(state => state.vehicles, shallow);
  const dispatchVehicleToTask = useAppStore(state => state.dispatchVehicleToTask);
  const cancelVehicleDispatch = useAppStore(state => state.cancelVehicleDispatch);
  
  // UI state
  const { selectedPlantId, taskStatusFilter, vehicleDisplayMode, setVehicleDisplayMode } = useUiStore();
  const { plants, productionLineCount, isLoadingPlants: isLoadingPlantInfo } = useCurrentPlantInfo();
  
  // Settings and modals
  const {
    settings, updateSetting, handleColumnVisibilityChange, handleColumnOrderChange,
    handleColumnTextStyleChange, handleColumnBackgroundChange,
    resetAllSettings, exportSettings, triggerImport, fileInputRef, isSettingsLoaded,
    isColumnVisibilityModalOpen, isColumnSpecificStyleModalOpen, editingColumnDef,
    openColumnVisibilityModal, closeColumnVisibilityModal, openColumnSpecificStyleModal, closeColumnSpecificStyleModal,
    isStyleEditorModalOpen, openStyleEditorModal, closeStyleEditorModal, handleVehiclesPerRowChange,
    handleToggleGroupCollapse,
    isGroupConfigModalOpen,
    setIsGroupConfigModalOpen,
  } = useTaskListSettings();

  // Destructure settings
  const { displayMode, density, enableZebraStriping, columnOrder, columnTextStyles, columnBackgrounds, inTaskVehicleCardStyles } = settings;

  // Context menus
  const {
    isTaskContextMenuOpen, taskContextMenuPosition, contextMenuTaskData,
    closeTaskContextMenu, openTaskContextMenu
  } = useTaskContextMenu();

  const {
    isVehicleCardContextMenuOpen, vehicleCardContextMenuPosition, vehicleCardContextMenuContext,
    closeVehicleCardContextMenu, openVehicleCardContextMenu
  } = useVehicleCardContextMenu();

  // Task card configuration
  const {
    taskCardConfig,
    isCardConfigLoaded,
    handleTaskCardConfigChange,
    handleResetTaskCardConfig,
    handleExportTaskCardConfig,
    handleImportTaskCardConfig,
  } = useTaskCardConfigManager();

  // Modal states
  const [isTankerNoteModalOpen, setIsTankerNoteModalOpen] = useState(false);
  const [selectedTaskForTankerNote, setSelectedTaskForTankerNote] = useState<Task | null>(null);
  const [isDeliveryOrderDetailsModalOpen, setIsDeliveryOrderDetailsModalOpen] = useState(false);
  const [selectedVehicleForDeliveryOrder, setSelectedVehicleForDeliveryOrder] = useState<Vehicle | null>(null);
  const [selectedTaskForDeliveryOrder, setSelectedTaskForDeliveryOrder] = useState<Task | null>(null);
  const [isReminderConfigModalOpen, setIsReminderConfigModalOpen] = useState(false);
  const [selectedTaskForReminderConfig, setSelectedTaskForReminderConfig] = useState<Task | null>(null);
  const [taskCardConfigModalOpen, setTaskCardConfigModalOpen] = useState(false);

  // Card view state
  const [dragOverTaskId, setDragOverTaskId] = useState<string | null>(null);
  const [dragOverProductionLineId, setDragOverProductionLineId] = useState<string | null>(null);

  // FAB
  const { fabPosition, isFabDragging, fabRef, handleFabContextMenu } = useDraggableFab({
    fabSize: FAB_SIZE,
    margin: FAB_MARGIN,
    storageKey: 'task-list-fab-position'
  });

  // Data processing
  const filteredTasks = useFilteredTasks();
  const tasksWithVehicles = useMemo(() => {
    const vehiclesByTask = new Map<string, Vehicle[]>();
    allVehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!vehiclesByTask.has(vehicle.assignedTaskId)) {
          vehiclesByTask.set(vehicle.assignedTaskId, []);
        }
        vehiclesByTask.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return filteredTasks.map(task => ({ ...task, vehicles: vehiclesByTask.get(task.id) || [] }));
  }, [filteredTasks, allVehicles]);

  const taskGroups = useMemo(() => {
    return groupTasks(tasksWithVehicles, settings.groupConfig);
  }, [
    tasksWithVehicles, 
    settings.groupConfig.enabled,
    settings.groupConfig.groupBy,
    settings.groupConfig.defaultCollapsed,
    settings.groupConfig.sortOrder
  ]);

  const currentDensityStyles = useMemo(() => {
    return getDensityStyles(settings.density);
  }, [settings.density]);

  const cardGridContainerClasses = getCardGridClasses(density);

  // Event handlers
  const eventHandlers = useTaskListEventHandlers({
    allTasks,
    plants,
    selectedPlantId,
    dispatchVehicleToTask,
    getStatusLabelProps: (status: string) => ({ label: status, variant: 'default' }), // Simplified
  });

  const dragHandlers = useTaskListDragHandlers();
  const fabHandlers = useTaskListFABHandlers();

  // Drop zone for task list
  const [{ isOverTaskList, canDropOnTaskList }] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: dragHandlers.handleTaskListDrop,
    collect: (monitor) => ({
      isOverTaskList: monitor.isOver(),
      canDropOnTaskList: monitor.canDrop(),
    }),
  });

  const taskListBoundaryRef = useRef<HTMLDivElement>(null);

  // This component is getting too large, let me continue in the next part...
  
  return null; // Placeholder for now
}
