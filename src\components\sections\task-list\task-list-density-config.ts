// src/components/sections/task-list/task-list-density-config.ts

import type { TaskListDensityMode, DensityStyleValues } from '@/types';

/**
 * Task list density style configuration
 * Defines visual styling for different density modes (loose, normal, compact)
 */
export const TASK_LIST_DENSITY_STYLE_CONFIG: Record<Exclude<TaskListDensityMode, 'table' | 'card'>, DensityStyleValues> = {
  loose: {
    headerPaddingX: 'px-2',
    headerPaddingY: 'py-1.5',
    headerHeight: 'h-7',
    headerFontSize: 'text-sm',
    cellPaddingX: 'px-2',
    cellPaddingY: 'py-1',
    cellFontSize: 'text-sm',
    cellFontWeight: 'font-normal',
    productionLineBoxSize: 'w-8 h-8',
    productionLineBoxFontSize: 'text-xs',
    productionLineBoxNumericWidth: 36,
    productionLineBoxGap: 6,
    cellHorizontalPaddingNumeric: 8,
  },
  normal: {
    headerPaddingX: 'px-1.5',
    headerPaddingY: 'py-1',
    headerHeight: 'h-6',
    headerFontSize: 'text-[12px]',
    cellPaddingX: 'px-1.5',
    cellPaddingY: 'py-0.5',
    cellFontSize: 'text-[12px]',
    cellFontWeight: 'font-normal',
    productionLineBoxSize: 'w-7 h-7',
    productionLineBoxFontSize: 'text-[10px]',
    productionLineBoxNumericWidth: 32,
    productionLineBoxGap: 4,
    cellHorizontalPaddingNumeric: 6,
  },
  compact: {
    headerPaddingX: 'px-1',
    headerPaddingY: 'py-0.5',
    headerHeight: 'h-5',
    headerFontSize: 'text-[12px]',
    cellPaddingX: 'px-1',
    cellPaddingY: 'py-0',
    cellFontSize: 'text-[12px]',
    cellFontWeight: 'font-normal',
    productionLineBoxSize: 'w-6 h-6',
    productionLineBoxFontSize: 'text-[10px]',
    productionLineBoxNumericWidth: 28,
    productionLineBoxGap: 2,
    cellHorizontalPaddingNumeric: 4,
  },
};

/**
 * Numeric values for density calculations
 * Used for calculating spacing, gaps, and padding in pixels
 */
export const TASK_LIST_DENSITY_NUMERIC_CONFIG = {
  compact: { 
    gap: 1, 
    cellHorizontalPadding: 2 * 2 
  },
  normal: { 
    gap: 2, 
    cellHorizontalPadding: 3 * 2 
  },
  loose: { 
    gap: 4, 
    cellHorizontalPadding: 4 * 2 
  }
};

/**
 * Card grid container classes for different density modes
 * Used in card view mode for spacing between cards
 */
export const TASK_LIST_CARD_GRID_CLASSES = {
  compact: "gap-px p-px",
  normal: "gap-0.5 p-0.5",
  loose: "gap-1 p-1"
} as const;

/**
 * Default density mode for task list
 */
export const DEFAULT_TASK_LIST_DENSITY: Exclude<TaskListDensityMode, 'table' | 'card'> = 'compact';

/**
 * Helper function to get density styles for a given density mode
 * @param density - The density mode
 * @returns The corresponding density style values
 */
export function getDensityStyles(density: Exclude<TaskListDensityMode, 'table' | 'card'>): DensityStyleValues {
  return TASK_LIST_DENSITY_STYLE_CONFIG[density] || TASK_LIST_DENSITY_STYLE_CONFIG.normal;
}

/**
 * Helper function to get numeric density config for a given density mode
 * @param density - The density mode
 * @returns The corresponding numeric density configuration
 */
export function getDensityNumericConfig(density: Exclude<TaskListDensityMode, 'table' | 'card'>) {
  return TASK_LIST_DENSITY_NUMERIC_CONFIG[density] || TASK_LIST_DENSITY_NUMERIC_CONFIG.normal;
}

/**
 * Helper function to get card grid classes for a given density mode
 * @param density - The density mode
 * @returns The corresponding card grid CSS classes
 */
export function getCardGridClasses(density: Exclude<TaskListDensityMode, 'table' | 'card'>): string {
  return TASK_LIST_CARD_GRID_CLASSES[density] || TASK_LIST_CARD_GRID_CLASSES.normal;
}
