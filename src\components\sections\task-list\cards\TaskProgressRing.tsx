'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface TaskProgressRingProps {
  percentage: number;
  completedVolume: number;
  requiredVolume: number;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  showLabel?: boolean;
  animated?: boolean;
  gradient?: boolean;
}

export const TaskProgressRing: React.FC<TaskProgressRingProps> = React.memo(({
  percentage,
  completedVolume,
  requiredVolume,
  size = 'medium',
  showLabel = true,
  animated = true,
  gradient = true,
}) => {
  // 获取尺寸配置
  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return {
          container: 'w-16 h-16',
          svg: 'w-16 h-16',
          strokeWidth: 3,
          radius: 26,
          textSize: 'text-xs',
          labelSize: 'text-[10px]',
        };
      case 'large':
        return {
          container: 'w-32 h-32',
          svg: 'w-32 h-32',
          strokeWidth: 6,
          radius: 58,
          textSize: 'text-xl',
          labelSize: 'text-sm',
        };
      case 'extra-large':
        return {
          container: 'w-40 h-40',
          svg: 'w-40 h-40',
          strokeWidth: 8,
          radius: 72,
          textSize: 'text-2xl',
          labelSize: 'text-base',
        };
      default: // medium
        return {
          container: 'w-24 h-24',
          svg: 'w-24 h-24',
          strokeWidth: 4,
          radius: 42,
          textSize: 'text-lg',
          labelSize: 'text-xs',
        };
    }
  };

  const config = getSizeConfig();
  const circumference = 2 * Math.PI * config.radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  // 获取进度颜色
  const getProgressColor = () => {
    if (percentage >= 90) return '#10b981'; // green-500
    if (percentage >= 70) return '#3b82f6'; // blue-500
    if (percentage >= 50) return '#f59e0b'; // amber-500
    if (percentage >= 30) return '#f97316'; // orange-500
    return '#ef4444'; // red-500
  };

  // 获取渐变ID
  const gradientId = `progress-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={cn(
      "relative flex items-center justify-center",
      config.container
    )}>
      {/* SVG 进度环 */}
      <svg
        className={cn(config.svg, "transform -rotate-90")}
        viewBox="0 0 100 100"
      >
        {/* 渐变定义 */}
        {gradient && (
          <defs>
            <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={getProgressColor()} stopOpacity="1" />
              <stop offset="100%" stopColor={getProgressColor()} stopOpacity="0.6" />
            </linearGradient>
          </defs>
        )}
        
        {/* 背景圆环 */}
        <circle
          cx="50"
          cy="50"
          r={config.radius}
          fill="none"
          stroke="currentColor"
          strokeWidth={config.strokeWidth}
          className="text-muted/20"
        />
        
        {/* 进度圆环 */}
        <circle
          cx="50"
          cy="50"
          r={config.radius}
          fill="none"
          stroke={gradient ? `url(#${gradientId})` : getProgressColor()}
          strokeWidth={config.strokeWidth}
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className={cn(
            "transition-all duration-1000 ease-out",
            animated && "animate-pulse"
          )}
          style={{
            filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.3))',
          }}
        />
      </svg>

      {/* 中心内容 */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
        {/* 百分比 */}
        <div className={cn(
          "font-bold leading-none",
          config.textSize,
          percentage >= 90 ? "text-green-600" :
          percentage >= 70 ? "text-blue-600" :
          percentage >= 50 ? "text-amber-600" :
          percentage >= 30 ? "text-orange-600" :
          "text-red-600"
        )}>
          {percentage}%
        </div>
        
        {/* 体积信息 */}
        {showLabel && (
          <div className={cn(
            "text-muted-foreground leading-tight mt-0.5",
            config.labelSize
          )}>
            <div>{completedVolume}</div>
            <div className="text-[10px] opacity-70">/ {requiredVolume}方</div>
          </div>
        )}
      </div>

      {/* 进度点 */}
      {percentage > 0 && (
        <div
          className="absolute w-2 h-2 bg-white rounded-full shadow-lg border-2 transition-all duration-1000 ease-out"
          style={{
            borderColor: getProgressColor(),
            transform: `rotate(${(percentage / 100) * 360 - 90}deg) translateX(${config.radius + 8}px)`,
            transformOrigin: '50% 50%',
          }}
        />
      )}

      {/* 发光效果 */}
      {percentage > 0 && animated && (
        <div
          className="absolute inset-0 rounded-full opacity-20 animate-ping"
          style={{
            background: `radial-gradient(circle, ${getProgressColor()}20 0%, transparent 70%)`,
          }}
        />
      )}
    </div>
  );
});

TaskProgressRing.displayName = 'TaskProgressRing';
